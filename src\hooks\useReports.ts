
import { useQuery } from '@tanstack/react-query';
import { getReportData, ReportData } from '@/data/reports';
import { TimeRange } from '@/components/dashboard/DashboardContent';
import { ReportType } from '@/components/pages/ReportsPage';

export function useReports(timeRange: TimeRange, reportType: ReportType) {
  return useQuery<ReportData, Error>({
    queryKey: ['reports', timeRange, reportType],
    queryFn: () => getReportData({ timeRange, reportType }),
    placeholderData: { main: [], secondary: [] },
  });
}
