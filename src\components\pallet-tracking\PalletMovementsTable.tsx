
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Truck } from "lucide-react";
import { PalletMovement, PalletStatus } from "@/data/palletTrackingData";
import { UseMutationResult } from "@tanstack/react-query";

interface PalletMovementsTableProps {
  movements: PalletMovement[];
  isLoading: boolean;
  search: string;
  setSearch: (value: string) => void;
  handleMarkAsDelivered: (id: string) => void;
  updateStatusMutation: UseMutationResult<void, Error, { id: string; status: PalletStatus; }, unknown>;
}

export const PalletMovementsTable = ({
  movements,
  isLoading,
  search,
  setSearch,
  handleMarkAsDelivered,
  updateStatusMutation
}: PalletMovementsTableProps) => {
    
  const filteredMovements = movements.filter((m) => {
    if (!search) return true;
    return (
      m.delivery_note.toLowerCase().includes(search.toLowerCase()) ||
      m.vehicle_registration.toLowerCase().includes(search.toLowerCase())
    );
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Truck size={20} />
          Pallet Tracking Overview
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex gap-4 mb-6">
          <div className="relative flex-1">
            <Search size={16} className="absolute left-3 top-3 text-slate-400" />
            <Input 
              placeholder="Search by delivery note, or vehicle reg..." 
              className="pl-10"
              value={search}
              onChange={e => setSearch(e.target.value)}
            />
          </div>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-4">Delivery Date</th>
                <th className="text-left py-3 px-4">Delivery Note</th>
                <th className="text-left py-3 px-4">Vehicle Reg</th>
                <th className="text-left py-3 px-4">Pallets Out</th>
                <th className="text-left py-3 px-4">Returned</th>
                <th className="text-left py-3 px-4">Status</th>
                <th className="text-left py-3 px-4">Actions</th>
              </tr>
            </thead>
            <tbody>
              {isLoading && (
                <tr><td colSpan={7} className="text-center py-8 text-slate-400">Loading...</td></tr>
              )}
              {!isLoading && filteredMovements.length === 0 && (
                <tr><td colSpan={7} className="text-center py-8 text-slate-400">No records found.</td></tr>
              )}
              {!isLoading && filteredMovements.map((pallet) => {
                const returnedCount = (pallet.returns || []).reduce(
                  (sum: number, ret: any) => sum + (ret.pallets_returned ?? 0),
                  0
                );
                return (
                  <tr key={pallet.id} className="border-b hover:bg-slate-50">
                    <td className="py-3 px-4">{pallet.delivery_date}</td>
                    <td className="py-3 px-4">{pallet.delivery_note}</td>
                    <td className="py-3 px-4">{pallet.vehicle_registration}</td>
                    <td className="py-3 px-4">{pallet.pallets_loaded}</td>
                    <td className="py-3 px-4">{returnedCount}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        pallet.status === 'Delivered' 
                          ? 'bg-green-100 text-green-800' 
                          : pallet.status === 'In Transit'
                          ? 'bg-blue-100 text-blue-800'
                          : pallet.status === 'Pending Return'
                          ? 'bg-yellow-100 text-yellow-800'
                          : pallet.status === 'Returned'
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-slate-100 text-slate-800'
                      }`}>
                        {pallet.status}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      {pallet.status === 'In Transit' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleMarkAsDelivered(pallet.id)}
                          disabled={updateStatusMutation.isPending}
                        >
                          Mark Delivered
                        </Button>
                      )}
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
};
