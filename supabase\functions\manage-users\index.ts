
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
import * as bcrypt from "https://deno.land/x/bcrypt@v0.4.1/mod.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get Supabase configuration
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';
    
    console.log('Edge Function invoked with URL:', supabaseUrl);
    
    if (!supabaseUrl) {
      console.error('SUPABASE_URL environment variable is not set');
      throw new Error('SUPABASE_URL environment variable is not set');
    }
    
    // Initialize Supabase client
    let supabase;
    if (!supabaseServiceKey) {
      console.log('Using anon key fallback for user management');
      const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY') ?? '';
      
      if (!supabaseAnonKey) {
        console.error('SUPABASE_ANON_KEY environment variable is not set');
        throw new Error('SUPABASE_ANON_KEY environment variable is not set');
      }
      
      supabase = createClient(supabaseUrl, supabaseAnonKey);
    } else {
      supabase = createClient(supabaseUrl, supabaseServiceKey);
    }

    // Safely parse the request body
    let requestBody;
    try {
      // Clone the request to log the raw body for debugging
      const clonedReq = req.clone();
      const bodyText = await clonedReq.text();
      console.log('Raw request body:', bodyText);
      
      // Parse the original request as JSON
      requestBody = await req.json();
      console.log('Parsed request body:', requestBody);
    } catch (e) {
      console.error('Error parsing request body:', e);
      throw new Error('Invalid JSON in request body');
    }
    
    const { action, userData, userId, passwords } = requestBody;
    
    if (!action) {
      throw new Error('Action is required');
    }
    
    console.log(`Processing action: ${action}`);
    
    switch (action) {
      case 'test': {
        // This is just a test endpoint to check if the function is accessible
        return new Response(JSON.stringify({ success: true, message: 'Edge Function is working!' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }
      
      case 'create': {
        const { username, full_name, email, password, role, active } = userData;
        
        // Hash the password
        const hashedPassword = await bcrypt.hash(password, 10);
        
        // Insert user into database
        const { data, error } = await supabase
          .from('users')
          .insert({
            username,
            full_name,
            email,
            password_hash: hashedPassword,
            role,
            active
          })
          .select()
          .single();

        if (error) {
          console.error('Error creating user:', error);
          throw new Error(error.message);
        }

        return new Response(JSON.stringify({ success: true, user: data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'update': {
        const { data, error } = await supabase
          .from('users')
          .update(userData)
          .eq('id', userId)
          .select()
          .single();

        if (error) {
          console.error('Error updating user:', error);
          throw new Error(error.message);
        }

        return new Response(JSON.stringify({ success: true, user: data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'delete': {
        const { error } = await supabase
          .from('users')
          .delete()
          .eq('id', userId);

        if (error) {
          console.error('Error deleting user:', error);
          throw new Error(error.message);
        }

        return new Response(JSON.stringify({ success: true }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'change-password': {
        const { currentPassword, newPassword } = passwords;

        console.log(`Attempting to change password for user ID: ${userId}`);

        if (!userId) {
          throw new Error('User ID is required for password change');
        }

        if (!currentPassword || !newPassword) {
          throw new Error('Both current and new passwords are required');
        }

        // First verify current password
        const { data: user, error: fetchError } = await supabase
          .from('users')
          .select('password_hash')
          .eq('id', userId)
          .single();

        if (fetchError) {
          console.error('Error fetching user:', fetchError);
          if (fetchError.code === 'PGRST116') {
            throw new Error('User not found');
          }
          throw new Error(`Database error: ${fetchError.message}`);
        }

        if (!user) {
          throw new Error('User not found');
        }

        console.log('User found, verifying password...');

        let isValidPassword = false;

        // Check if password hash is bcrypt (starts with $2b$) or SHA-256
        if (user.password_hash.startsWith('$2b$')) {
          // This is a bcrypt hash
          isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
        } else {
          // This is a SHA-256 hash (users created via Settings UI)
          const encoder = new TextEncoder();
          const data = encoder.encode(currentPassword);
          const hashBuffer = await crypto.subtle.digest('SHA-256', data);
          const hashArray = Array.from(new Uint8Array(hashBuffer));
          const currentPasswordHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
          isValidPassword = user.password_hash === currentPasswordHash;
        }

        if (!isValidPassword) {
          console.log('Password verification failed');
          throw new Error('Current password is incorrect');
        }

        console.log('Password verified, updating with new password...');

        // Hash new password using bcrypt for consistency
        const hashedPassword = await bcrypt.hash(newPassword, 10);

        const { error } = await supabase
          .from('users')
          .update({ password_hash: hashedPassword })
          .eq('id', userId);

        if (error) {
          console.error('Error changing password:', error);
          throw new Error(`Failed to update password: ${error.message}`);
        }

        console.log('Password updated successfully');

        return new Response(JSON.stringify({ success: true }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      default:
        throw new Error(`Invalid action: ${action}`);
    }
  } catch (error) {
    console.error('Error in manage-users function:', error);
    return new Response(JSON.stringify({ error: error.message, stack: error.stack }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});
