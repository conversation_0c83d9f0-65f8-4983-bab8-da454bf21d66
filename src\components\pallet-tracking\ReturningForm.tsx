
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar, Clipboard } from "lucide-react";
import { PalletMovement } from "@/data/palletTrackingData";

export interface ReturnFormState {
  pallet_id: string;
  return_date: string;
  pallets_returned: string;
  condition: string;
  comments: string;
}

interface ReturningFormProps {
  formState: ReturnFormState;
  handleSelectChange: (field: keyof ReturnFormState, value: string) => void;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  movements: PalletMovement[];
}

export const ReturningForm = ({ formState, handleSelectChange, handleChange, movements }: ReturningFormProps) => (
  <div className="space-y-4 mt-4">
    <div className="grid gap-2">
      <Label htmlFor="pallet_id">Select Delivery</Label>
      <Select value={formState.pallet_id} onValueChange={val => handleSelectChange('pallet_id', val)}>
        <SelectTrigger id="pallet_id">
          <SelectValue placeholder="Select delivery to return pallets for" />
        </SelectTrigger>
        <SelectContent>
          {movements
            .filter(p => {
              const returned = (p.returns || []).reduce((sum: number, r: any) => sum + (r.pallets_returned ?? 0), 0);
              return returned < p.pallets_loaded;
            })
            .map(pallet => (
              <SelectItem key={pallet.id} value={pallet.id}>
                {pallet.delivery_note} ({pallet.delivery_date})
              </SelectItem>
            ))}
        </SelectContent>
      </Select>
    </div>
    <div className="grid grid-cols-2 gap-4">
      <div className="grid gap-2">
        <Label htmlFor="return_date">
          <Calendar size={16} className="inline mr-2" />
          Return Date
        </Label>
        <Input id="return_date" type="date" value={formState.return_date} onChange={handleChange} />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="pallets_returned">
          <Clipboard size={16} className="inline mr-2" />
          Pallets Returned
        </Label>
        <Input id="pallets_returned" type="number" min="1" placeholder="Number of pallets" value={formState.pallets_returned} onChange={handleChange} />
      </div>
    </div>
    <div className="grid gap-2">
      <Label htmlFor="condition">Condition</Label>
      <Select value={formState.condition} onValueChange={val => handleSelectChange('condition', val)}>
        <SelectTrigger id="condition">
          <SelectValue placeholder="Select condition of returned pallets" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="good">Good - All pallets intact</SelectItem>
          <SelectItem value="minor-damage">Minor Damage - Usable with repairs</SelectItem>
          <SelectItem value="major-damage">Major Damage - Some unusable</SelectItem>
          <SelectItem value="unusable">Unusable - All pallets damaged</SelectItem>
        </SelectContent>
      </Select>
    </div>
    <div className="grid gap-2">
      <Label htmlFor="comments">Comments</Label>
      <Textarea id="comments" placeholder="Add notes about the returned pallets" value={formState.comments} onChange={handleChange} />
    </div>
  </div>
);
