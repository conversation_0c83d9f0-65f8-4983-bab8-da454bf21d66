import { supabase } from "@/integrations/supabase/client";
import { getShiftType } from "@/utils/shiftHelpers";
import { getTeamsWithMembers, TeamWithMembers } from "./teamData";
import { getManagementBrickTypes, ManagementBrickType } from "./managementBrickTypes";
import { DehackingEntryFromSupabase } from "./dehackingStore";
import { SettingProductionEntry } from "./settingProductionStore";
import { Payment } from "./paymentsData";

export interface EarningComponent {
  type: 'Dehacking' | 'Setting';
  date: string;
  sourceId: string | number;
  amount: number;
  details: string;
}

export interface PendingEarnings {
  employeeId: number;
  employeeName: string;
  total: number;
  breakdown: EarningComponent[];
}

export interface EmployeePeriodEarnings {
  employeeId: number;
  employeeName: string;
  total: number;
  settingTotal: number;
  dehackingTotal: number;
}

export const calculateGrossEarningsForPeriod = async ({ from, to }: { from: string, to: string }): Promise<EmployeePeriodEarnings[]> => {
    const [allBrickTypes, allTeamsWithMembers, dehackingEntries, settingEntries, allEmployees] = await Promise.all([
        getManagementBrickTypes(),
        getTeamsWithMembers(),
        supabase.from('dehacking_entries').select('*').gte('date', from).lte('date', to).then(res => res.data || []),
        supabase.from('setting_production_entries').select('*').gte('date', from).lte('date', to).then(res => res.data || []),
        supabase.from('employees').select('id, name').then(res => res.data || []),
    ]);

    const earningsByEmployee: Record<number, EmployeePeriodEarnings> = {};

    (allEmployees as {id: number, name: string}[]).forEach(emp => {
        earningsByEmployee[emp.id] = { employeeId: emp.id, employeeName: emp.name, total: 0, settingTotal: 0, dehackingTotal: 0 };
    });

    // Dehacking earnings
    for (const entry of (dehackingEntries as DehackingEntryFromSupabase[])) {
        if (!earningsByEmployee[entry.employee_id]) continue;
        const brickType = allBrickTypes.find(bt => bt.id === entry.brick_type_id);
        if (!brickType) continue;

        const hourString = `${String(entry.hour).padStart(2, '0')}:00`;
        const shift = getShiftType(hourString);
        const rate = shift === 'day' ? (brickType.dehacking_day_rate || brickType.dehacking_rate) : (brickType.dehacking_night_rate || brickType.dehacking_rate);
        
        if (!rate) continue;

        const earningAmount = entry.pallet_count * Number(rate);
        earningsByEmployee[entry.employee_id].total += earningAmount;
        earningsByEmployee[entry.employee_id].dehackingTotal += earningAmount;
    }

    // Setting earnings
    for (const entry of (settingEntries as SettingProductionEntry[])) {
        const brickType = allBrickTypes.find(bt => bt.id === entry.brick_type_id);
        if (!brickType) continue;

        const team = allTeamsWithMembers.find(t => t.id === entry.team_id);
        if (!team || team.members.length === 0) continue;

        const rate = brickType.setting_day_rate || brickType.setting_rate;
        if (!rate) continue;

        const totalEarning = entry.pallet_count * Number(rate);
        const earningPerMember = totalEarning / team.members.length;

        for (const member of team.members) {
            if (earningsByEmployee[member.id]) {
                earningsByEmployee[member.id].total += earningPerMember;
                earningsByEmployee[member.id].settingTotal += earningPerMember;
            }
        }
    }

    return Object.values(earningsByEmployee);
};

export const calculateAllPendingEarnings = async (): Promise<PendingEarnings[]> => {
  const { data: allBrickTypesData, error: brickTypesError } = await supabase.from('management_brick_types').select('*');
  const { data: allDehackingEntriesData, error: dehackingError } = await supabase.from('dehacking_entries').select('*');
  const { data: allSettingEntriesData, error: settingError } = await supabase.from('setting_production_entries').select('*');
  const { data: allPaymentsData, error: paymentsError } = await supabase.from('payments').select('*');
  const { data: allEmployeesData, error: employeesError } = await supabase.from('employees').select('id, name');
  const allTeamsWithMembers = await getTeamsWithMembers();
  
  if (brickTypesError || dehackingError || settingError || paymentsError || employeesError) {
    console.error("Error fetching data for earnings calculation", { brickTypesError, dehackingError, settingError, paymentsError, employeesError });
    throw new Error("Could not fetch data for earnings calculation.");
  }

  const allBrickTypes: ManagementBrickType[] = allBrickTypesData || [];
  const allDehackingEntries: DehackingEntryFromSupabase[] = allDehackingEntriesData || [];
  const allSettingEntries: SettingProductionEntry[] = allSettingEntriesData || [];
  const allPayments: Payment[] = allPaymentsData || [];
  const allEmployees: {id: number, name: string}[] = allEmployeesData || [];

  const earningsByEmployee: { [employeeId: number]: { total: number; breakdown: EarningComponent[] } } = {};

  allEmployees.forEach(emp => {
    earningsByEmployee[emp.id] = { total: 0, breakdown: [] };
  });

  // Calculate Dehacking Earnings
  for (const entry of allDehackingEntries) {
    const brickType = allBrickTypes.find(bt => bt.id === entry.brick_type_id);
    if (!brickType || !earningsByEmployee[entry.employee_id]) continue;

    const hourString = `${String(entry.hour).padStart(2, '0')}:00`;
    const shift = getShiftType(hourString);
    const rate = shift === 'day' ? (brickType.dehacking_day_rate || brickType.dehacking_rate) : (brickType.dehacking_night_rate || brickType.dehacking_rate);
    
    if (!rate) continue;

    const earningAmount = entry.pallet_count * Number(rate);
    earningsByEmployee[entry.employee_id].total += earningAmount;
    earningsByEmployee[entry.employee_id].breakdown.push({
      type: 'Dehacking',
      date: entry.date,
      sourceId: entry.id,
      amount: earningAmount,
      details: `${entry.pallet_count} pallets of '${brickType.name}' (${shift})`,
    });
  }

  // Calculate Setting Earnings
  for (const entry of allSettingEntries) {
    const brickType = allBrickTypes.find(bt => bt.id === entry.brick_type_id);
    if (!brickType) continue;

    const team = allTeamsWithMembers.find(t => t.id === entry.team_id);
    if (!team || team.members.length === 0) continue;

    // Assuming day rate for setting as there's no time information.
    const rate = brickType.setting_day_rate || brickType.setting_rate;
    if (!rate) continue;

    const totalEarning = entry.pallet_count * Number(rate);
    const earningPerMember = totalEarning / team.members.length;

    for (const member of team.members) {
      if (earningsByEmployee[member.id]) {
        earningsByEmployee[member.id].total += earningPerMember;
        earningsByEmployee[member.id].breakdown.push({
          type: 'Setting',
          date: entry.date,
          sourceId: entry.id,
          amount: earningPerMember,
          details: `${entry.pallet_count} pallets by team '${team.name}' / ${team.members.length} members`,
        });
      }
    }
  }

  // Subtract already paid amounts for production work
  for (const payment of allPayments) {
    if ((payment.payment_type === 'Dehacking' || payment.payment_type === 'Setting') && earningsByEmployee[payment.employee_id]) {
      earningsByEmployee[payment.employee_id].total -= Number(payment.amount);
    }
  }

  return Object.entries(earningsByEmployee)
    .map(([employeeId, data]) => ({
      employeeId: Number(employeeId),
      employeeName: allEmployees.find(e => e.id === Number(employeeId))?.name || 'Unknown',
      total: data.total,
      breakdown: data.breakdown,
    }))
    .filter(e => e.total > 0.01) // Only return employees with pending earnings
    .sort((a, b) => b.total - a.total);
};
