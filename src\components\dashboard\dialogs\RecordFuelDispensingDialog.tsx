import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { logFuelTransaction } from "@/data/fuelTransactionsStore";
import { calculateLitresPerHour, calculateKmPerLitre } from "@/utils/fuelCalculations";

interface RecordFuelDispensingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  bunkers: { id: string; name: string }[];
  assets: { id: string; name: string }[];
}

// Asset type detection for demo - in real use, use asset metadata!
function detectAssetType(assetName: string) {
  const lower = assetName.toLowerCase();
  if (lower.includes("kiln") || lower.includes("generator") || lower.includes("forklift")) return "machinery";
  if (lower.includes("truck") || lower.includes("vehicle") || lower.includes("car")) return "vehicle";
  return "machinery"; // fallback
}

export const RecordFuelDispensingDialog = ({
  isOpen,
  onClose,
  bunkers,
  assets,
}: RecordFuelDispensingDialogProps) => {
  const [selectedBunker, setSelectedBunker] = useState<string>("");
  const [selectedAsset, setSelectedAsset] = useState<string>("");
  const [startReading, setStartReading] = useState(""); // HR or KM
  const [endReading, setEndReading] = useState("");     // HR or KM
  const [litresFilled, setLitresFilled] = useState("");
  const [date, setDate] = useState<Date | undefined>(new Date());

  // Determine asset type if asset is selected
  const assetType = selectedAsset ? detectAssetType(
    assets.find(a => a.id === selectedAsset)?.name || ""
  ) : null;

  let calculationResult: string | null = null;
  if (
    assetType === "machinery" && startReading && endReading && litresFilled
  ) {
    const lph = calculateLitresPerHour(Number(startReading), Number(endReading), Number(litresFilled));
    calculationResult = lph !== null ? `Litres/Hour: ${lph.toFixed(2)}` : null;
  }
  if (
    assetType === "vehicle" && startReading && endReading && litresFilled
  ) {
    const kpl = calculateKmPerLitre(Number(startReading), Number(endReading), Number(litresFilled));
    calculationResult = kpl !== null ? `Km/Litre: ${kpl.toFixed(2)}` : null;
  }

  const handleSubmit = () => {
    logFuelTransaction({
      id: Date.now().toString(),
      type: "dispensing",
      date: date || new Date(),
      bunkerId: selectedBunker,
      assetId: selectedAsset,
      assetType: assetType || "machinery",
      startReading: Number(startReading),
      endReading: Number(endReading),
      litresFilled: Number(litresFilled),
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Record Fuel Transaction</DialogTitle>
          <DialogDescription>Enter fuel dispensing details</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="fuelBunker">Fuel Bunker</Label>
              <Select value={selectedBunker} onValueChange={setSelectedBunker}>
                <SelectTrigger id="fuelBunker">
                  <SelectValue placeholder="Select bunker" />
                </SelectTrigger>
                <SelectContent>
                  {bunkers.map((bunker) => (
                    <SelectItem key={bunker.id} value={bunker.id}>
                      {bunker.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="asset">Asset</Label>
              <Select value={selectedAsset} onValueChange={setSelectedAsset}>
                <SelectTrigger id="asset">
                  <SelectValue placeholder="Select asset" />
                </SelectTrigger>
                <SelectContent>
                  {assets.map((asset) => (
                    <SelectItem key={asset.id} value={asset.id}>
                      {asset.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          {/* Machinery and Vehicle specific fields */}
          {assetType && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startReading">
                  {assetType === "machinery" ? "Start Hours" : "Start KM"}
                </Label>
                <Input
                  id="startReading"
                  type="number"
                  value={startReading}
                  onChange={e => setStartReading(e.target.value)}
                  placeholder={assetType === "machinery" ? "e.g., 1240.5" : "e.g., 32190"}
                />
              </div>
              <div>
                <Label htmlFor="endReading">
                  {assetType === "machinery" ? "End Hours" : "End KM"}
                </Label>
                <Input
                  id="endReading"
                  type="number"
                  value={endReading}
                  onChange={e => setEndReading(e.target.value)}
                  placeholder={assetType === "machinery" ? "e.g., 1248.0" : "e.g., 32260"}
                />
              </div>
            </div>
          )}
          <div>
            <Label htmlFor="litresFilled">Amount of Litres Filled</Label>
            <Input
              id="litresFilled"
              type="number"
              value={litresFilled}
              onChange={e => setLitresFilled(e.target.value)}
              placeholder="e.g., 45"
            />
          </div>
          <div>
            <Label htmlFor="date">Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "yyyy/MM/dd") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 pointer-events-auto" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          {calculationResult && (
            <div className="rounded bg-slate-100 p-2 text-sm text-slate-700 border">
              <strong>Calculated:</strong> {calculationResult}
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={
            !selectedBunker ||
            !selectedAsset ||
            !startReading ||
            !endReading ||
            !litresFilled
          }>Record Transaction</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
