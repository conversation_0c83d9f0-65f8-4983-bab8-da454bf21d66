
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAllPalletMovements, createPalletMovement, recordPalletReturn, updatePalletMovementStatus, PalletStatus } from "@/data/palletTrackingData";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { DashboardStats } from "../pallet-tracking/DashboardStats";
import { PalletMovementsTable } from "../pallet-tracking/PalletMovementsTable";
import { TrackPalletDialog } from "../pallet-tracking/TrackPalletDialog";
import { ReturnFormState } from "../pallet-tracking/ReturningForm";

export const PalletTrackingPage = () => {
  const [isTrackingDialogOpen, setIsTrackingDialogOpen] = useState(false);
  const [search, setSearch] = useState("");
  // Form state (outgoing)
  const [outgoingForm, setOutgoingForm] = useState({
    delivery_date: "",
    delivery_note: "",
    vehicle_registration: "",
    destination: "",
    pallets_loaded: "",
    comments: ""
  });
  // Form state (returning)
  const [returnForm, setReturnForm] = useState<ReturnFormState>({
    pallet_id: "",
    return_date: "",
    pallets_returned: "",
    condition: "",
    comments: ""
  });

  const queryClient = useQueryClient();

  // Live querying of all pallet movements
  const { data: movements = [], isLoading } = useQuery({
    queryKey: ["pallet-movements"],
    queryFn: getAllPalletMovements,
  });

  // Real-time updates
  useEffect(() => {
    const channel = supabase
      .channel('pallet-tracking-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'pallet_movements' },
        () => {
          queryClient.invalidateQueries({ queryKey: ['pallet-movements'] });
        }
      )
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'pallet_returns' },
        () => {
          queryClient.invalidateQueries({ queryKey: ['pallet-movements'] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  // Dashboard metrics
  const dashboard = {
    total: movements.reduce((sum, m) => sum + m.pallets_loaded, 0),
    inTransit: movements.filter(m => m.status === "In Transit").reduce((sum, m) => sum + m.pallets_loaded, 0),
    delivered: movements.filter(m => m.status === "Delivered" || m.status === "Returned").reduce((sum, m) => sum + m.pallets_loaded, 0),
    pendingReturn: movements.filter(m => m.status === "Pending Return").reduce((sum, m) => sum + m.pallets_loaded, 0),
  };
  // Outgoing creation
  const outgoingMutation = useMutation({
    mutationFn: createPalletMovement,
    onSuccess: () => {
      toast.success("Outgoing pallet recorded!");
      setIsTrackingDialogOpen(false);
      setOutgoingForm({
        delivery_date: "",
        delivery_note: "",
        vehicle_registration: "",
        destination: "",
        pallets_loaded: "",
        comments: "",
      });
      queryClient.invalidateQueries({ queryKey: ["pallet-movements"] });
    },
    onError: (err: any) => {
      toast.error("Could not record outgoing pallet: " + err?.message);
    },
  });

  // Pallet return mutation
  const returnMutation = useMutation({
    mutationFn: recordPalletReturn,
  });

  // Pallet status update mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, status }: { id: string; status: PalletStatus }) => updatePalletMovementStatus(id, status),
    onSuccess: () => {
      toast.success("Pallet status updated!");
      // No need to invalidate here, the realtime subscription will handle it.
    },
    onError: (err: any) => {
      toast.error("Could not update pallet status: " + err?.message);
    },
  });

  // Handle form input for outgoing
  const handleOutgoingChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setOutgoingForm(f => ({ ...f, [e.target.id]: e.target.value }));
  };
  // Handle form input for returning
  const handleReturnChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setReturnForm(f => ({ ...f, [e.target.id]: e.target.value }));
  };
  const handleReturnSelectChange = (field: keyof ReturnFormState, value: string) => {
    setReturnForm(f => ({...f, [field]: value}));
  }

  // Outgoing submit
  const handleOutgoingSubmit = () => {
    // Validate
    if (
      !outgoingForm.delivery_date ||
      !outgoingForm.delivery_note ||
      !outgoingForm.vehicle_registration ||
      !outgoingForm.destination ||
      !outgoingForm.pallets_loaded
    ) {
      toast.error("Please fill all fields");
      return;
    }
    if (Number(outgoingForm.pallets_loaded) < 1) {
      toast.error("Number of pallets must be at least 1");
      return;
    }
    outgoingMutation.mutate({
      delivery_date: outgoingForm.delivery_date,
      delivery_note: outgoingForm.delivery_note,
      vehicle_registration: outgoingForm.vehicle_registration,
      product_type: "N/A", // Default value
      destination: outgoingForm.destination,
      pallets_loaded: Number(outgoingForm.pallets_loaded),
      comments: outgoingForm.comments,
    });
  };

  // Return submit
  const handleReturnSubmit = () => {
    if (
      !returnForm.pallet_id ||
      !returnForm.return_date ||
      !returnForm.pallets_returned ||
      !returnForm.condition
    ) {
      toast.error("Please fill all fields");
      return;
    }
    const movement = movements.find(m => m.id === returnForm.pallet_id);
    if (!movement) {
      toast.error("Selected delivery not found");
      return;
    }
    const returnedSoFar =
      (movement.returns || []).reduce((sum: number, r: any) => sum + (r.pallets_returned || 0), 0);
    const remaining = Number(movement.pallets_loaded) - returnedSoFar;
    const palletsBeingReturned = Number(returnForm.pallets_returned);
    if (palletsBeingReturned < 1 || palletsBeingReturned > remaining) {
      toast.error(`Return quantity must be between 1 and ${remaining}`);
      return;
    }

    returnMutation.mutate({
      pallet_movement_id: movement.id,
      return_date: returnForm.return_date,
      pallets_returned: palletsBeingReturned,
      condition: returnForm.condition,
      comments: returnForm.comments,
    }, {
      onSuccess: () => {
        toast.success("Pallet return recorded!");
        setIsTrackingDialogOpen(false);
        setReturnForm({
          pallet_id: "",
          return_date: "",
          pallets_returned: "",
          condition: "",
          comments: "",
        });

        const newTotalReturned = returnedSoFar + palletsBeingReturned;
        const palletsLoaded = Number(movement.pallets_loaded);
        let newStatus: PalletStatus;

        if (newTotalReturned >= palletsLoaded) {
          newStatus = "Returned";
        } else {
          newStatus = "Pending Return";
        }

        if (movement.status !== newStatus) {
          updateStatusMutation.mutate({ id: movement.id, status: newStatus });
        } else {
          queryClient.invalidateQueries({ queryKey: ["pallet-movements"] });
        }
      },
      onError: (err: any) => {
        toast.error("Could not record returned pallets: " + err?.message);
      },
    });
  };

  // Mark as delivered handler
  const handleMarkAsDelivered = (id: string) => {
    updateStatusMutation.mutate({ id, status: 'Delivered' });
  };


  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Pallet Tracking</h1>
          <p className="text-slate-600">Monitor pallet locations and delivery status</p>
        </div>
        <Button 
          className="bg-slate-800 hover:bg-slate-700"
          onClick={() => setIsTrackingDialogOpen(true)}
        >
          <Plus size={20} className="mr-2" />
          Track New Pallet
        </Button>
      </div>

      <DashboardStats stats={dashboard} />

      <PalletMovementsTable
        movements={movements}
        isLoading={isLoading}
        search={search}
        setSearch={setSearch}
        handleMarkAsDelivered={handleMarkAsDelivered}
        updateStatusMutation={updateStatusMutation}
      />
      
      <TrackPalletDialog 
        isOpen={isTrackingDialogOpen}
        onOpenChange={setIsTrackingDialogOpen}
        movements={movements}
        outgoingForm={outgoingForm}
        handleOutgoingChange={handleOutgoingChange}
        handleOutgoingSubmit={handleOutgoingSubmit}
        returnForm={returnForm}
        handleReturnChange={handleReturnChange}
        handleReturnSelectChange={handleReturnSelectChange}
        handleReturnSubmit={handleReturnSubmit}
        outgoingMutation={outgoingMutation}
        returnMutation={returnMutation}
        updateStatusMutation={updateStatusMutation}
      />
    </div>
  );
};
