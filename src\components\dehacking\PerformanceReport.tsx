
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON> } from "lucide-react";
import { PeriodSelector } from "./PeriodSelector";

interface PerformanceReportData {
  employeeId: number;
  employee: string;
  totalPallets: number;
  earnings: string;
  brickTypes: string;
  date: string;
}

interface PerformanceReportProps {
  selectedPeriod: "daily" | "weekly" | "monthly";
  onPeriodChange: (period: "daily" | "weekly" | "monthly") => void;
  filteredData: PerformanceReportData[];
}

export const PerformanceReport = ({ 
  selectedPeriod, 
  onPeriodChange, 
  filteredData 
}: PerformanceReportProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Hammer size={20} />
          Dehacking Performance Report
        </CardTitle>
        <p className="text-slate-600 text-sm">View dehacking performance metrics for individual employees</p>
      </CardHeader>
      <CardContent>
        <PeriodSelector 
          selectedPeriod={selectedPeriod}
          onPeriodChange={onPeriodChange}
        />

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-slate-200">
                <th className="text-left py-3 px-4 font-medium text-slate-600">Employee</th>
                <th className="text-left py-3 px-4 font-medium text-slate-600">Total Pallets</th>
                <th className="text-left py-3 px-4 font-medium text-slate-600">Earnings</th>
                <th className="text-left py-3 px-4 font-medium text-slate-600">Brick Types</th>
              </tr>
            </thead>
            <tbody>
              {filteredData.length > 0 ? (
                filteredData.map((row, index) => (
                  <tr key={index} className="border-b border-slate-200 hover:bg-slate-50">
                    <td className="py-3 px-4 text-slate-800 font-medium">{row.employee}</td>
                    <td className="py-3 px-4 text-slate-700">{row.totalPallets}</td>
                    <td className="py-3 px-4 text-slate-700">{row.earnings}</td>
                    <td className="py-3 px-4 text-slate-700">{row.brickTypes}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="text-center py-12 text-slate-500">
                    No dehacking data found for this period or for the selected filters.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};
