
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface SystemConfig {
  id: string;
  company_name: string;
  timezone: string;
  currency: string;
  auto_backup: boolean;
  theme: string;
  language: string;
  dashboard_layout: string;
}

export function useSystemConfig() {
  return useQuery({
    queryKey: ["systemConfig"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("system_config")
        .select("*")
        .single();
      
      if (error) throw new Error(error.message);
      return data as SystemConfig;
    }
  });
}

export function useUpdateSystemConfig() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (config: Partial<SystemConfig>) => {
      const { data, error } = await supabase
        .from("system_config")
        .update(config)
        .select()
        .single();
      
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["systemConfig"] });
    }
  });
}
