
import { useState, useMemo, useEffect } from "react";
import { Card, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableHead, TableBody, TableRow, TableCell } from "@/components/ui/table";
import { getFuelBunkers, type FuelBunker } from "@/data/fuelBunkersData";
import { getFuelTransactions } from "@/data/fuelTransactionsStore";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { format } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";

export const FuelTransactionReport = () => {
  const { data: fuelBunkersData = [], isLoading: isLoadingBunkers } = useQuery<FuelBunker[]>({
    queryKey: ['fuelBunkers'],
    queryFn: getFuelBunkers,
  });

  const [selectedBunker, setSelectedBunker] = useState("");

  useEffect(() => {
    if (fuelBunkersData.length > 0 && !selectedBunker) {
      setSelectedBunker(fuelBunkersData[0].id);
    }
  }, [fuelBunkersData, selectedBunker]);

  const txns = useMemo(
    () =>
      getFuelTransactions()
        .filter((t) => ("bunkerId" in t) && t.bunkerId === selectedBunker)
        .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()),
    [selectedBunker]
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fuel Transaction Report</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex gap-2 items-center">
          <span className="text-sm text-slate-600">Bunker:</span>
          <Select value={selectedBunker} onValueChange={setSelectedBunker} disabled={isLoadingBunkers || !selectedBunker}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select bunker" />
            </SelectTrigger>
            <SelectContent>
              {fuelBunkersData.map((b) => (
                <SelectItem value={b.id} key={b.id}>
                  {b.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="overflow-x-auto">
          {isLoadingBunkers ? (
             <div className="flex justify-center items-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-slate-500" />
            </div>
          ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Asset / Supplier</TableHead>
                <TableHead>Litres In</TableHead>
                <TableHead>Litres Out</TableHead>
                <TableHead>Info</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {txns.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-sm text-slate-500 text-center">
                    No transactions found for this bunker.
                  </TableCell>
                </TableRow>
              ) : (
                txns.map((t) =>
                  t.type === "delivery" ? (
                    <TableRow key={t.id}>
                      <TableCell>{format(new Date(t.date), "yyyy-MM-dd")}</TableCell>
                      <TableCell>Delivery</TableCell>
                      <TableCell>{t.supplier} (Invoice: {t.invoiceNumber})</TableCell>
                      <TableCell>{t.quantity.toLocaleString()}</TableCell>
                      <TableCell>-</TableCell>
                      <TableCell>Cost/L: R{t.costPerLiter}</TableCell>
                    </TableRow>
                  ) : (
                    <TableRow key={t.id}>
                      <TableCell>{format(new Date(t.date), "yyyy-MM-dd")}</TableCell>
                      <TableCell>Dispensing</TableCell>
                      <TableCell>{t.assetId}</TableCell>
                      <TableCell>-</TableCell>
                      <TableCell>{t.litresFilled.toLocaleString()}</TableCell>
                      <TableCell>
                        Start: {t.startReading}
                        {t.endReading !== undefined ? `, End: ${t.endReading}` : ""}
                      </TableCell>
                    </TableRow>
                  )
                )
              )}
            </TableBody>
          </Table>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
