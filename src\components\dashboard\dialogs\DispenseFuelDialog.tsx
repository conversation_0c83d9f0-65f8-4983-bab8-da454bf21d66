
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { recordFuelDispensingTransaction } from "@/data/fuelDispensingTransactions";
import { useAssets } from "@/hooks/useAssets";
import { useEmployees } from "@/hooks/useEmployees";
import { FuelBunkerSelect } from "@/components/fuel/FuelBunkerSelect";
import { FuelBunkerRadioGroup } from "@/components/fuel/FuelBunkerRadioGroup";
import { useQueryClient } from "@tanstack/react-query";

interface DispenseFuelDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const DispenseFuelDialog = ({
  isOpen,
  onClose
}: DispenseFuelDialogProps) => {
  const queryClient = useQueryClient();
  const [tab, setTab] = useState<"delivery" | "dispensing">("dispensing");
  // Form state
  const [bunkerId, setBunkerId] = useState("");
  const [assetId, setAssetId] = useState("");
  const [operatorId, setOperatorId] = useState("");
  const [startReading, setStartReading] = useState("");
  const [endReading, setEndReading] = useState("");
  const [litres, setLitres] = useState("");
  const [transDate, setTransDate] = useState<Date | undefined>(new Date());
  const [notes, setNotes] = useState("");
  const [loading, setLoading] = useState(false);

  const { toast } = useToast();

  // Hooks for fetching data
  const { data: assets = [], isLoading: isLoadingAssets } = useAssets();
  const { data: employees = [], isLoading: isLoadingEmps } = useEmployees();

  const handleSubmit = async () => {
    setLoading(true);
    try {
      if (!bunkerId || !assetId || !operatorId || !litres || !transDate) {
        toast({ title: "Error", description: "Please complete all required fields.", variant: "destructive" });
        setLoading(false);
        return;
      }
      await recordFuelDispensingTransaction({
        fuel_bunker_id: bunkerId,
        asset_id: assetId,
        operator_id: Number(operatorId),
        transaction_date: format(transDate, "yyyy-MM-dd"),
        starting_reading: startReading ? Number(startReading) : null,
        ending_reading: endReading ? Number(endReading) : null,
        quantity_liters: Number(litres),
        notes: notes || null,
      });
      toast({ title: "Success", description: "Fuel dispensing recorded!" });
      queryClient.invalidateQueries({ queryKey: ["fuelBunkers"] });
      onClose();
    } catch (e: any) {
      toast({ title: "Error recording transaction", description: e.message || e.toString(), variant: "destructive" });
    }
    setLoading(false);
  };

  useEffect(() => {
    if (!isOpen) {
      setBunkerId("");
      setAssetId("");
      setOperatorId("");
      setStartReading("");
      setEndReading("");
      setTransDate(new Date());
      setLitres("");
      setNotes("");
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <div className="flex justify-between items-center">
            <Tabs defaultValue="dispensing" value={tab} onValueChange={v => setTab(v as "dispensing" | "delivery")}>
              <TabsList className="grid grid-cols-2 w-80">
                <TabsTrigger value="delivery" disabled>Record Delivery</TabsTrigger>
                <TabsTrigger value="dispensing">Dispense Fuel</TabsTrigger>
              </TabsList>
            </Tabs>
            <Button
              type="button"
              variant="ghost"
              className="text-xs h-7 px-2"
              onClick={() => setTab(tab === "dispensing" ? "delivery" : "dispensing")}
              disabled={tab === "delivery"}
            >
              Switch to Delivery
            </Button>
          </div>
          <DialogTitle className="text-xl mt-2">Dispense Fuel</DialogTitle>
          <DialogDescription>Enter dispensing details below.</DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FuelBunkerRadioGroup value={bunkerId} onChange={setBunkerId} />
          <div>
            <Label htmlFor="asset">Asset</Label>
            <Select value={assetId} onValueChange={setAssetId} disabled={isLoadingAssets}>
              <SelectTrigger id="asset">
                <SelectValue placeholder={isLoadingAssets ? "Loading..." : "Select asset"} />
              </SelectTrigger>
              <SelectContent>
                {assets.map((a: any) => (
                  <SelectItem key={a.id} value={a.id}>{a.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-2">
          <div>
            <Label htmlFor="startReading">Start Reading</Label>
            <Input
              id="startReading"
              type="number"
              value={startReading}
              min="0"
              onChange={e => setStartReading(e.target.value)}
              placeholder="e.g. 12345"
            />
          </div>
          <div>
            <Label htmlFor="endReading">End Reading</Label>
            <Input
              id="endReading"
              type="number"
              value={endReading}
              min="0"
              onChange={e => setEndReading(e.target.value)}
              placeholder="e.g. 12400"
            />
          </div>
          <div>
            <Label htmlFor="litres">Litres Dispensed</Label>
            <Input
              id="litres"
              type="number"
              value={litres}
              min="0"
              onChange={e => setLitres(e.target.value)}
              placeholder="e.g. 50"
            />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
          <div>
            <Label htmlFor="operator">Operator</Label>
            <Select value={operatorId} onValueChange={setOperatorId} disabled={isLoadingEmps}>
              <SelectTrigger id="operator">
                <SelectValue placeholder={isLoadingEmps ? "Loading..." : "Select operator"} />
              </SelectTrigger>
              <SelectContent>
                {employees.map(o => (
                  <SelectItem key={o.id} value={String(o.id)}>{o.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="date">Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !transDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {transDate ? format(transDate, "yyyy/MM/dd") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 pointer-events-auto" align="start">
                <Calendar
                  mode="single"
                  selected={transDate}
                  onSelect={setTransDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div className="mt-2">
          <Label htmlFor="notes">Notes (optional)</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={e => setNotes(e.target.value)}
            placeholder="Add any additional notes..."
            rows={2}
            className="resize-none"
          />
        </div>
        <DialogFooter className="space-y-2 mt-2">
          <Button variant="outline" onClick={onClose} disabled={loading}>Cancel</Button>
          <Button className="bg-blue-600 hover:bg-blue-700" onClick={handleSubmit} disabled={
            loading ||
            !bunkerId ||
            !assetId ||
            !operatorId ||
            !litres ||
            !transDate
          }>
            {loading ? "Saving..." : "Record Fuel Dispensing"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
