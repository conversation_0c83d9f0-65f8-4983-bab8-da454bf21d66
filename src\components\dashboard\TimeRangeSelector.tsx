
import { TimeRange } from "./DashboardContent";

interface TimeRangeSelectorProps {
  selectedRange: TimeRange;
  onRangeChange: (range: TimeRange) => void;
}

export const TimeRangeSelector = ({ selectedRange, onRangeChange }: TimeRangeSelectorProps) => {
  const ranges: { value: TimeRange; label: string }[] = [
    { value: "today", label: "Today" },
    { value: "week", label: "This Week" },
    { value: "month", label: "This Month" },
    { value: "year", label: "This Year" },
  ];

  return (
    <div className="bg-white rounded-lg border border-slate-200 p-4">
      <h3 className="text-lg font-semibold text-slate-800 mb-4">Time Range</h3>
      <div className="flex flex-wrap gap-4">
        {ranges.map((range) => (
          <label key={range.value} className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name="timeRange"
              value={range.value}
              checked={selectedRange === range.value}
              onChange={() => onRangeChange(range.value)}
              className="w-4 h-4 text-slate-600"
            />
            <span className="text-slate-700">{range.label}</span>
          </label>
        ))}
      </div>
    </div>
  );
};
