
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "../ui/dialog";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

// Add new prop type for optional success callback
interface AddBrickTypeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export interface BrickTypeFormData {
  name: string;
  category: string;
  grade: string;
  settingRate: number;
  dehackingRate: number;
  overtimeRate: number;
  brickStage: string;
}

const AddBrickTypeDialog: React.FC<AddBrickTypeDialogProps> = ({ 
  open, 
  onOpenChange,
  onSuccess
}) => {
  const [formData, setFormData] = useState<BrickTypeFormData>({
    name: '',
    category: '',
    grade: '',
    settingRate: 0,
    dehackingRate: 0,
    overtimeRate: 0,
    brickStage: 'finished'
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (field: keyof BrickTypeFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Insert into Supabase management_brick_types table
  const handleSubmit = async () => {
    setLoading(true);
    // Cheap id (this should be more robust, using e.g. uuid in production)
    const id = formData.name.toLowerCase().replace(/[^a-z0-9]/g, '_') + "_" + Date.now();
    const payload = {
      id,
      name: formData.name,
      category: formData.category,
      grade: formData.grade,
      setting_rate: formData.settingRate,
      dehacking_rate: formData.dehackingRate,
      overtime_rate: formData.overtimeRate,
      status: 'Active', // default
      bricks_per_pallet: 0, // You may allow entering this if needed
      brick_stage: formData.brickStage,
    };
    const { error } = await supabase.from('management_brick_types').insert([payload]);

    setLoading(false);

    if (error) {
      toast.error("Failed to create brick type: " + error.message);
      return;
    }
    toast.success("Brick type created successfully");
    onOpenChange(false);
    setFormData({
      name: '',
      category: '',
      grade: '',
      settingRate: 0,
      dehackingRate: 0,
      overtimeRate: 0,
      brickStage: 'finished'
    });
    if (onSuccess) onSuccess();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add New Brick Type</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="brickName">Brick Name</label>
            <Input 
              id="brickName" 
              placeholder="Enter brick type name" 
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
            />
          </div>
          
          {/* Select brick stage */}
          <div className="grid gap-2">
            <label htmlFor="brickStage">Brick Stage</label>
            <Select 
              value={formData.brickStage} 
              onValueChange={(value) => handleChange('brickStage', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select brick stage" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="extruded">Extruded (Imperial/Maxi for output & settings)</SelectItem>
                <SelectItem value="finished">Finished (for dehacking/products)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <label htmlFor="category">Category</label>
              <Select 
                value={formData.category} 
                onValueChange={(value) => handleChange('category', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Facing">Facing</SelectItem>
                  <SelectItem value="Engineering">Engineering</SelectItem>
                  <SelectItem value="Decorative">Decorative</SelectItem>
                  <SelectItem value="Paving">Paving</SelectItem>
                  <SelectItem value="Extruded">Extruded</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="grade">Grade</label>
              <Select 
                value={formData.grade} 
                onValueChange={(value) => handleChange('grade', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select grade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A+">A+</SelectItem>
                  <SelectItem value="A">A</SelectItem>
                  <SelectItem value="B">B</SelectItem>
                  <SelectItem value="C">C</SelectItem>
                  <SelectItem value="2nd Grade">2nd Grade</SelectItem>
                  <SelectItem value="NFP">NFP</SelectItem>
                  <SelectItem value="NFX">NFX</SelectItem>
                  <SelectItem value="Blue">Blue</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="grid gap-2">
            <label htmlFor="settingRate">Setting Rate (₹ per pallet)</label>
            <Input 
              id="settingRate" 
              type="number" 
              value={formData.settingRate.toString()}
              onChange={(e) => handleChange('settingRate', parseFloat(e.target.value) || 0)}
            />
          </div>
          
          <div className="grid gap-2">
            <label htmlFor="dehackingRate">Dehacking Rate (₹ per pallet)</label>
            <Input 
              id="dehackingRate" 
              type="number" 
              value={formData.dehackingRate.toString()}
              onChange={(e) => handleChange('dehackingRate', parseFloat(e.target.value) || 0)}
            />
          </div>
          
          <div className="grid gap-2">
            <label htmlFor="overtimeRate">Overtime Rate (₹ per pallet)</label>
            <Input 
              id="overtimeRate" 
              type="number" 
              value={formData.overtimeRate.toString()}
              onChange={(e) => handleChange('overtimeRate', parseFloat(e.target.value) || 0)}
            />
          </div>
        </div>
        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? 'Creating...' : 'Create Brick Type'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddBrickTypeDialog;
