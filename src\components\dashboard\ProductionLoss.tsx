
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { AlertTriangle } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";
import { supabase } from "@/integrations/supabase/client";

export const ProductionLoss = () => {
  const { data: brickTypes = [], isLoading: isLoadingBrickTypes } = useQuery<ManagementBrickType[]>({
    queryKey: ["managementBrickTypes"],
    queryFn: getManagementBrickTypes,
  });

  const { data: productionEntries, isLoading: isLoadingProd } = useQuery({
      queryKey: ['productionForLoss'],
      queryFn: async () => (await supabase.from('production_entries').select('brick_type_id, pallet_count')).data ?? [],
  });

  const { data: settingProductionEntries, isLoading: isLoadingSet } = useQuery({
      queryKey: ['settingProductionForLoss'],
      queryFn: async () => (await supabase.from('setting_production_entries').select('brick_type_id, pallet_count')).data ?? [],
  });
  
  const { data: dehackingEntries, isLoading: isLoadingDehack } = useQuery({
      queryKey: ['dehackingForLoss'],
      queryFn: async () => (await supabase.from('dehacking_entries').select('brick_type_id, pallet_count')).data ?? [],
  });

  const brickTypeMap = useMemo(() => {
    const map = new Map<string, number>();
    brickTypes.forEach(bt => {
      map.set(bt.id, bt.bricks_per_pallet);
    });
    return map;
  }, [brickTypes]);

  const factoryOutput = useMemo(() => {
    return (productionEntries || []).reduce((sum, entry) => {
      const bpp = brickTypeMap.get(entry.brick_type_id) || 0;
      return sum + entry.pallet_count * bpp;
    }, 0);
  }, [productionEntries, brickTypeMap]);

  const settingTotal = useMemo(() => {
    return (settingProductionEntries || []).reduce((sum, entry) => {
      const bpp = brickTypeMap.get(entry.brick_type_id) || 0;
      return sum + entry.pallet_count * bpp;
    }, 0);
  }, [settingProductionEntries, brickTypeMap]);

  const dehackingTotal = useMemo(() => {
    return (dehackingEntries || []).reduce((sum, entry) => {
      const bpp = brickTypeMap.get(entry.brick_type_id) || 0;
      return sum + entry.pallet_count * bpp;
    }, 0);
  }, [dehackingEntries, brickTypeMap]);

  const greenBrickLoss = factoryOutput - settingTotal;
  const breakageLoss = settingTotal - dehackingTotal;
  const totalLoss = factoryOutput - dehackingTotal;

  const valueDisplay = (value: number) => (
    <span className={value > 0 ? "text-red-600 font-bold" : "text-green-700 font-bold"}>
      {value.toLocaleString()}
    </span>
  );
  
  const isLoading = isLoadingBrickTypes || isLoadingProd || isLoadingSet || isLoadingDehack;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-800 flex items-center gap-2">
          <AlertTriangle size={20} className="text-orange-500" />
          Production Loss
        </CardTitle>
        <p className="text-sm text-slate-600">Live losses across the production process</p>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="py-6 text-center text-slate-500">Loading...</div>
        ) : (
          <div className="space-y-6">
            <div>
              <div className="font-semibold mb-2 text-slate-700 border-b pb-1">Green Brick Loss</div>
              <p>
                {valueDisplay(greenBrickLoss)} bricks<br />
                <span className="text-xs text-slate-500">Difference between Factory Output and Setting Teams</span>
              </p>
            </div>
            <div>
              <div className="font-semibold mb-2 text-slate-700 border-b pb-1">Breakage Loss</div>
              <p>
                {valueDisplay(breakageLoss)} bricks<br />
                <span className="text-xs text-slate-500">Difference between Setting Teams and Dehacking</span>
              </p>
            </div>
            <div>
              <div className="font-semibold mb-2 text-slate-700 border-b pb-1">Total Loss</div>
              <p>
                {valueDisplay(totalLoss)} bricks<br />
                <span className="text-xs text-slate-500">Difference between Factory Output and Dehacking</span>
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
