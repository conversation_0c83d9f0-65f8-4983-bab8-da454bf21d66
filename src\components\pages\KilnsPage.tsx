
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Flame, Settings, Activity, Loader2 } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { getKilns, updateKilnStatus, KilnConfig } from "@/data/kilnData";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import ChamberDashboard from "../dashboard/ChamberDashboard";

export const KilnsPage = () => {
  const queryClient = useQueryClient();

  const { data: kilns = [], isLoading, isError } = useQuery<KilnConfig[]>({
    queryKey: ['kilns'],
    queryFn: getKilns,
  });

  const { mutate: toggleKilnStatusMutation, isPending: isUpdating } = useMutation({
    mutationFn: updateKilnStatus,
    onSuccess: () => {
      toast.success("Kiln status updated successfully!");
      queryClient.invalidateQueries({ queryKey: ['kilns'] });
    },
    onError: (error) => {
      toast.error(`Failed to update kiln status: ${error.message}`);
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
      case "operational":
        return "bg-green-100 text-green-800";
      case "inactive":
      case "offline":
        return "bg-red-100 text-red-800";
      case "maintenance":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-slate-100 text-slate-800";
    }
  };

  const handleToggleKilnStatus = (kiln: KilnConfig) => {
    const newStatus = kiln.status === "operational" ? "maintenance" : "operational";
    toggleKilnStatusMutation({ id: kiln.id, status: newStatus });
  };

  // DEBUG logging
  console.log("[DEBUG] kilns:", kilns);

  const activeFiresCount = kilns.reduce((total, kiln) => total + (kiln.fires?.filter(f => f.status === "active").length || 0), 0);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Kiln Management</h1>
          <p className="text-slate-600">Manage kilns, fires, and view chamber status.</p>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Kilns Overview</TabsTrigger>
          <TabsTrigger value="chambers">Chamber Dashboard</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="mt-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
              <p className="ml-2 text-slate-500">Loading kilns...</p>
            </div>
          ) : isError ? (
            <div className="text-center py-12 text-red-500">
              <p>Failed to load kiln data. Please try again later.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Key metrics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="text-2xl font-bold text-slate-800">{kilns.length}</div>
                    <p className="text-slate-600">Total Kilns</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <div className="text-2xl font-bold text-green-600">
                      {kilns.filter(k => k.status === "operational").length}
                    </div>
                    <p className="text-slate-600">Operational</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <div className="text-2xl font-bold text-blue-600">
                      {activeFiresCount}
                    </div>
                    <p className="text-slate-600">Active Fires</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardContent className="p-6">
                    <div className="text-2xl font-bold text-yellow-600">
                      {kilns.filter(k => k.status === "maintenance").length}
                    </div>
                    <p className="text-slate-600">In Maintenance</p>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Flame size={20} />
                    Kilns Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {kilns.map((kiln) => (
                      <Card key={kiln.id} className="border-l-4 border-l-blue-500">
                        <CardHeader className="pb-3">
                          <div className="flex justify-between items-start">
                            <CardTitle className="text-lg">{kiln.name}</CardTitle>
                            <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(kiln.status)}`}>
                              {kiln.status}
                            </span>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div>
                            <h4 className="font-medium text-sm text-slate-600 mb-2">Fires ({kiln.fires?.length ?? 0}{kiln.fires?.length === 1 ? '' : '/2'})</h4>
                            <div className="space-y-2">
                              {(kiln.fires ?? []).map((fire) => (
                                <div key={fire.id} className="flex justify-between items-center p-2 bg-slate-50 rounded">
                                  <span className="text-sm">{fire.name}</span>
                                  <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(fire.status)}`}>
                                    {fire.status}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                          <div className="flex gap-2 pt-2">
                            <Button variant="outline" size="sm" className="flex-1" onClick={() => handleToggleKilnStatus(kiln)} disabled={isUpdating}>
                              <Settings size={14} className="mr-1" />
                              Toggle Status
                            </Button>
                            <Button variant="outline" size="sm" className="flex-1" disabled>
                              <Activity size={14} className="mr-1" />
                              Monitor
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                     {kilns.length === 0 && (
                      <div className="col-span-full text-center py-12 text-slate-500">
                        <p>No kilns found. Please add kilns to the database.</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
        <TabsContent value="chambers">
          <ChamberDashboard />
        </TabsContent>
      </Tabs>
    </div>
  );
};
