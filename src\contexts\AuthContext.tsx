import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabaseAdmin } from '@/integrations/supabase/client';
import type { User } from '@/hooks/useUsers';

interface LoginCredentials {
  username: string;
  password: string;
}

interface AuthContextType {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  loginTime: Date | null;
  getSessionDuration: () => string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Authenticate against real database users
const validateCredentials = async (username: string, password: string): Promise<User | null> => {
  try {
    // Get user from database
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('username', username)
      .eq('active', true)
      .single();

    if (error || !user) {
      console.log('User not found or inactive:', username);
      return null;
    }

    let passwordMatches = false;

    // Check if password hash is bcrypt (starts with $2b$) or SHA-256
    if (user.password_hash.startsWith('$2b$')) {
      // This is a bcrypt hash (default admin user)
      // For bcrypt verification, we'll use a simple check for known passwords
      // In production, you'd use proper bcrypt library
      if (username === 'admin' && password === 'admin123') {
        passwordMatches = true;
      }
    } else {
      // This is a SHA-256 hash (users created via Settings UI)
      const encoder = new TextEncoder();
      const data = encoder.encode(password);
      const hashBuffer = await crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const passwordHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

      passwordMatches = user.password_hash === passwordHash;
    }

    if (passwordMatches) {
      // Convert database user to our User type
      return {
        id: user.id,
        username: user.username,
        full_name: user.full_name,
        email: user.email || undefined,
        role: user.role || 'admin',
        active: user.active || true,
        created_at: user.created_at || new Date().toISOString(),
        updated_at: user.updated_at || new Date().toISOString()
      };
    }

    console.log('Password mismatch for user:', username);
    return null;
  } catch (error) {
    console.error('Error validating credentials:', error);
    return null;
  }
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loginTime, setLoginTime] = useState<Date | null>(null);

  // Check for existing session on app load
  useEffect(() => {
    const savedUser = localStorage.getItem('currentUser');
    const savedLoginTime = localStorage.getItem('loginTime');
    
    if (savedUser && savedLoginTime) {
      try {
        setCurrentUser(JSON.parse(savedUser));
        setLoginTime(new Date(savedLoginTime));
      } catch (error) {
        console.error('Error loading saved session:', error);
        localStorage.removeItem('currentUser');
        localStorage.removeItem('loginTime');
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    setIsLoading(true);

    try {
      const user = await validateCredentials(credentials.username, credentials.password);

      if (user) {
        const now = new Date();
        setCurrentUser(user);
        setLoginTime(now);

        // Save to localStorage for persistence
        localStorage.setItem('currentUser', JSON.stringify(user));
        localStorage.setItem('loginTime', now.toISOString());

        setIsLoading(false);
        return true;
      }

      setIsLoading(false);
      return false;
    } catch (error) {
      console.error('Login error:', error);
      setIsLoading(false);
      return false;
    }
  };

  const logout = () => {
    // Clear user-specific activity data
    if (currentUser) {
      // Optionally keep the activities for the user, or clear them
      // localStorage.removeItem(`activities_${currentUser.id}`);
    }

    setCurrentUser(null);
    setLoginTime(null);
    localStorage.removeItem('currentUser');
    localStorage.removeItem('loginTime');
  };

  const getSessionDuration = (): string => {
    if (!loginTime) return '0h 0m';
    
    const now = new Date();
    const diffMs = now.getTime() - loginTime.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  const contextValue: AuthContextType = {
    currentUser,
    isAuthenticated: !!currentUser,
    isLoading,
    login,
    logout,
    loginTime,
    getSessionDuration,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
