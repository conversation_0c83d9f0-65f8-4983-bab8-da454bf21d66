
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

export type Fire = Database['public']['Tables']['fires']['Row'];

export const getFires = async (): Promise<Fire[]> => {
  const { data, error } = await supabase.from('fires').select('*');
  if (error) {
    console.error("Error fetching fires", error);
    throw new Error(error.message);
  }
  return data || [];
};
