import React, { useEffect, useState, useSyncExternalStore } from "react";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { getKilns, KilnConfig, FireConfig } from "@/data/kilnData";
import { getSettingProductionEntries, subscribeToSettingProduction, SettingProductionEntry } from "@/data/settingProductionStore";
import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";

type ChamberSummary = {
  chamberId: string;
  chamberName: string;
  setBricks: number;
  dehackedBricks: number;
};

function aggregateChamberSummary(
  fires: FireConfig[],
  brickTypes: ManagementBrickType[],
  settingProductionEntries: SettingProductionEntry[],
) {
  // chamberId = fire.id

  // Gather per chamber: {chamberId, chamberName, setBricks, dehackedBricks}
  return fires.map((fire) => {
    // Setting
    const setEntries = settingProductionEntries.filter(e => e.fire_id === fire.id);
    const setBricks = setEntries.reduce((sum, entry) => {
      const brickType = brickTypes.find(bt => bt.id === entry.brick_type_id);
      return sum + (brickType ? brickType.bricks_per_pallet * entry.pallet_count : 0);
    }, 0);
    // Dehacking cannot be calculated per chamber with current schema.
    const dehackedBricks = 0;

    return {
      chamberId: fire.id,
      chamberName: fire.name,
      setBricks,
      dehackedBricks,
    };
  });
}

export const ChamberDashboard = () => {
  const [kilns, setKilns] = useState<KilnConfig[]>([]);
  const [brickTypes, setBrickTypes] = useState<ManagementBrickType[]>([]);
  const [loading, setLoading] = useState(true);

  const settingEntries = useSyncExternalStore(subscribeToSettingProduction, getSettingProductionEntries);

  useEffect(() => {
    async function loadData() {
      setLoading(true);
      const [kilnsResult, brickTypesResult] = await Promise.all([
        getKilns(),
        getManagementBrickTypes(),
      ]);
      setKilns(kilnsResult);
      setBrickTypes(brickTypesResult);
      setLoading(false);
    }
    loadData();
  }, []);

  if (loading) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Chamber Dashboard</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-slate-500">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-8">
      {kilns.map(kiln => {
        const chambers = kiln.fires;
        // Always display 12 chambers, fill with blanks if less
        const CHAMBER_COUNT = 12;
        const chambersList = [
          ...chambers,
          ...Array(CHAMBER_COUNT - chambers.length).fill(null),
        ].slice(0, CHAMBER_COUNT);

        // Get summary
        const summaryById = aggregateChamberSummary(chambers, brickTypes, settingEntries);
        // Fill empty chambers with blanks for grid
        const chamberData: (ChamberSummary | null)[] = chambersList.map((fire, idx) =>
          fire
            ? summaryById.find(cs => cs.chamberId === fire.id) || {
                chamberId: fire.id,
                chamberName: fire.name,
                setBricks: 0,
                dehackedBricks: 0,
              }
            : null
        );
        return (
          <Card key={kiln.id}>
            <CardHeader>
              <CardTitle>{kiln.name} - Chamber Dashboard</CardTitle>
            </CardHeader>
            <CardContent>
              {/* Setting Table */}
              <h3 className="font-semibold text-slate-800 mb-2">Setting</h3>
              <div className="grid grid-cols-4 gap-4 mb-4">
                {chamberData.map((chamber, idx) => (
                  <div
                    key={chamber ? chamber.chamberId : idx}
                    className={`border rounded p-2 min-h-[40px] text-center bg-slate-50 flex flex-col items-center justify-center`}
                  >
                    <div className="text-sm font-medium">
                      {chamber
                        ? `Chamber ${idx + 1}${chamber.chamberName ? ` (${chamber.chamberName})` : ""}`
                        : `Chamber ${idx + 1}`}
                    </div>
                    <div className="text-blue-700 font-bold text-xl">{chamber?.setBricks.toLocaleString() || "-"}</div>
                  </div>
                ))}
              </div>
              {/* Dehacking Table */}
              <h3 className="font-semibold text-slate-800 mb-2">Dehacking</h3>
              <div className="grid grid-cols-4 gap-4">
                {chamberData.map((chamber, idx) => (
                  <div
                    key={chamber ? chamber.chamberId : idx}
                    className={`border rounded p-2 min-h-[40px] text-center bg-slate-50 flex flex-col items-center justify-center`}
                  >
                    <div className="text-sm font-medium">
                      {chamber
                        ? `Chamber ${idx + 1}${chamber.chamberName ? ` (${chamber.chamberName})` : ""}`
                        : `Chamber ${idx + 1}`}
                    </div>
                    <div className="text-green-700 font-bold text-xl">{chamber?.dehackedBricks.toLocaleString() || "-"}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default ChamberDashboard;
