
import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useUnassignedEmployees, useAddTeamMember } from "@/hooks/useTeams";
import { TeamWithMembers } from '@/data/teamData';
import { Loader2 } from 'lucide-react';

interface AddMemberDialogProps {
  isOpen: boolean;
  onClose: () => void;
  team: TeamWithMembers;
}

export const AddMemberDialog = ({ isOpen, onClose, team }: AddMemberDialogProps) => {
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(null);
  const { data: employees, isLoading } = useUnassignedEmployees();
  const { mutate: addMember, isPending } = useAddTeamMember();

  const handleAddMember = () => {
    if (selectedEmployeeId) {
      addMember({ teamId: team.id, employeeId: parseInt(selectedEmployeeId) }, {
        onSuccess: onClose
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Member to {team.name}</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <Select onValueChange={setSelectedEmployeeId} disabled={isLoading || isPending}>
            <SelectTrigger>
              <SelectValue placeholder="Select an employee" />
            </SelectTrigger>
            <SelectContent>
              {isLoading && <div className="flex items-center justify-center p-2"><Loader2 className="h-4 w-4 animate-spin" /></div>}
              {employees?.map(emp => (
                <SelectItem key={emp.id} value={String(emp.id)}>{emp.name}</SelectItem>
              ))}
              {employees?.length === 0 && !isLoading && <div className="p-2 text-center text-sm text-gray-500">No unassigned employees.</div>}
            </SelectContent>
          </Select>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleAddMember} disabled={!selectedEmployeeId || isPending}>
            {isPending ? "Adding..." : "Add Member"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
