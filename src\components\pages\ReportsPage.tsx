import { useState } from "react";
import { TimeRange } from "@/components/dashboard/DashboardContent";
import { ReportPreviewDialog } from "@/components/reports/ReportPreviewDialog";
import { ForkliftPerformanceDashboard } from "@/components/dashboard/ForkliftPerformanceDashboard";
import { useReports } from "@/hooks/useReports";
import { ReportHeader } from "@/components/reports/ReportHeader";
import { ReportControls } from "@/components/reports/ReportControls";
import { ReportChart } from "@/components/reports/ReportChart";

export type ReportType = 
  | "factory-output" 
  | "setting-teams" 
  | "dehacking" 
  | "fuel-management" 
  | "employee-hours";

const reportTypes = [
  { value: "factory-output" as ReportType, label: "Factory Output" },
  { value: "setting-teams" as ReportType, label: "Setting Teams" },
  { value: "dehacking" as ReportType, label: "Dehacking" },
  { value: "fuel-management" as ReportType, label: "Fuel Management" },
  { value: "employee-hours" as ReportType, label: "Employee Hours & Earnings" },
];

export const ReportsPage = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRange>("month");
  const [selectedReportType, setSelectedReportType] = useState<ReportType>("factory-output");
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const { data: reportData, isLoading, isError } = useReports(selectedTimeRange, selectedReportType);

  const handleGenerateReport = () => {
    setIsPreviewOpen(true);
  };

  return (
    <div className="space-y-6">
      <ReportHeader onGenerateReport={handleGenerateReport} isLoading={isLoading} />

      <ReportControls
        selectedTimeRange={selectedTimeRange}
        onTimeRangeChange={setSelectedTimeRange}
        selectedReportType={selectedReportType}
        onReportTypeChange={setSelectedReportType}
        reportTypes={reportTypes}
      />

      <ReportChart
        reportData={reportData}
        isLoading={isLoading}
        isError={isError}
        selectedReportType={selectedReportType}
        selectedTimeRange={selectedTimeRange}
        reportTypes={reportTypes}
      />

      <ForkliftPerformanceDashboard />

      <ReportPreviewDialog
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        reportData={reportData}
        timeRange={selectedTimeRange}
        reportType={selectedReportType}
        reportTitle={reportTypes.find(type => type.value === selectedReportType)?.label || "Report"}
      />
    </div>
  );
};

export default ReportsPage;
