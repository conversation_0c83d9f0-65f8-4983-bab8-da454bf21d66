
project_id = "hhxwnoreclckmtenugmt"

[api]
enabled = true
port = 54321
schemas = ["public", "storage", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
port = 54324
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
refresh_token_reuse_interval = 10
enable_signup = true

[auth.external.apple]
enabled = false
client_id = ""
secret = ""

[auth.external.azure]
enabled = false
client_id = ""
secret = ""

[auth.external.bitbucket]
enabled = false
client_id = ""
secret = ""

[auth.external.discord]
enabled = false
client_id = ""
secret = ""

[auth.external.facebook]
enabled = false
client_id = ""
secret = ""

[auth.external.github]
enabled = false
client_id = ""
secret = ""

[auth.external.gitlab]
enabled = false
client_id = ""
secret = ""

[auth.external.google]
enabled = false
client_id = ""
secret = ""

[auth.external.keycloak]
enabled = false
client_id = ""
secret = ""
url = ""

[auth.external.linkedin]
enabled = false
client_id = ""
secret = ""

[auth.external.notion]
enabled = false
client_id = ""
secret = ""

[auth.external.twitch]
enabled = false
client_id = ""
secret = ""

[auth.external.twitter]
enabled = false
client_id = ""
secret = ""

[auth.external.slack]
enabled = false
client_id = ""
secret = ""

[auth.external.spotify]
enabled = false
client_id = ""
secret = ""

[auth.external.workos]
enabled = false
client_id = ""
secret = ""

[auth.external.zoom]
enabled = false
client_id = ""
secret = ""

[db]
port = 54322

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
port = 54323

[storage]
enabled = true
port = 54324
file_size_limit = "50MiB"

[functions.manage-users]
verify_jwt = false

[edge_runtime]
policy = "per_worker"

[analytics]
enabled = false
