
import { Menu, LogOut, Clock, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import { useActivityTracking } from "@/hooks/useActivityTracking";
import { Badge } from "@/components/ui/badge";

interface HeaderProps {
  onToggleSidebar: () => void;
}

export const Header = ({ onToggleSidebar }: HeaderProps) => {
  const { currentUser, logout, getSessionDuration, loginTime } = useAuth();
  const { getActivitySummary } = useActivityTracking();

  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const activitySummary = getActivitySummary();

  const handleLogout = () => {
    if (window.confirm('Are you sure you want to log out?')) {
      logout();
    }
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'factory_supervisor': return 'Factory Supervisor';
      case 'yard_supervisor': return 'Yard Supervisor';
      case 'admin': return 'Administrator';
      case 'manager': return 'Manager';
      case 'finance': return 'Finance';
      default: return role;
    }
  };

  return (
    <header className="bg-white border-b border-slate-200 px-6 py-4 flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleSidebar}
          className="lg:hidden"
        >
          <Menu size={20} />
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-slate-800">Welcome, {currentUser?.full_name}</h1>
          <p className="text-sm text-slate-600">Dashboard Overview | {currentDate}</p>
        </div>
      </div>

      {/* User Info and Session Details */}
      <div className="flex items-center gap-4">
        {/* Activity Summary */}
        <div className="hidden md:flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm text-slate-600">
            <Clock size={16} />
            <span>Session: {getSessionDuration()}</span>
          </div>
          <Badge variant="secondary" className="text-xs">
            {activitySummary.totalActivities} activities today
          </Badge>
        </div>

        {/* User Info */}
        <div className="flex items-center gap-3 px-3 py-2 bg-slate-50 rounded-lg">
          <User size={16} className="text-slate-600" />
          <div className="text-sm">
            <div className="font-medium text-slate-800">{currentUser?.full_name}</div>
            <div className="text-slate-500">{getRoleDisplayName(currentUser?.role || '')}</div>
          </div>
        </div>

        {/* Logout Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleLogout}
          className="flex items-center gap-2"
        >
          <LogOut size={16} />
          <span className="hidden sm:inline">Logout</span>
        </Button>
      </div>
    </header>
  );
};
