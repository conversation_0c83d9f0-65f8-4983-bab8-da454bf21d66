
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, Truck } from "lucide-react";
import { OutgoingForm } from "./OutgoingForm";
import { ReturningForm, ReturnFormState } from "./ReturningForm";
import { PalletMovement } from "@/data/palletTrackingData";
import { UseMutationResult } from "@tanstack/react-query";

interface TrackPalletDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  movements: PalletMovement[];
  
  outgoingForm: any;
  handleOutgoingChange: (e: any) => void;
  handleOutgoingSubmit: () => void;
  
  returnForm: any;
  handleReturnChange: (e: any) => void;
  handleReturnSelectChange: (field: keyof ReturnFormState, value: string) => void;
  handleReturnSubmit: () => void;

  outgoingMutation: UseMutationResult<any, any, any, any>;
  returnMutation: UseMutationResult<any, any, any, any>;
  updateStatusMutation: UseMutationResult<any, any, any, any>;
}

export const TrackPalletDialog = ({
  isOpen,
  onOpenChange,
  movements,
  outgoingForm,
  handleOutgoingChange,
  handleOutgoingSubmit,
  returnForm,
  handleReturnChange,
  handleReturnSelectChange,
  handleReturnSubmit,
  outgoingMutation,
  returnMutation,
  updateStatusMutation,
}: TrackPalletDialogProps) => {
  const [activeTab, setActiveTab] = useState("outgoing");

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Truck size={20} />
            Track Pallet Movement
          </DialogTitle>
        </DialogHeader>
        <Tabs defaultValue="outgoing" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="outgoing" className="flex items-center gap-2">
              <ArrowRight size={16} />
              Outgoing Pallets
            </TabsTrigger>
            <TabsTrigger value="returning" className="flex items-center gap-2">
              <ArrowLeft size={16} />
              Returning Pallets
            </TabsTrigger>
          </TabsList>
          <TabsContent value="outgoing" className="mt-4">
            <OutgoingForm formState={outgoingForm} handleChange={handleOutgoingChange} />
          </TabsContent>
          <TabsContent value="returning" className="mt-4">
            <ReturningForm 
              formState={returnForm}
              handleChange={handleReturnChange}
              handleSelectChange={handleReturnSelectChange}
              movements={movements}
            />
          </TabsContent>
        </Tabs>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button
            className="bg-slate-800 hover:bg-slate-700"
            disabled={
              outgoingMutation.isPending ||
              returnMutation.isPending ||
              updateStatusMutation.isPending
            }
            onClick={activeTab === "outgoing" ? handleOutgoingSubmit : handleReturnSubmit}
          >
            {(outgoingMutation.isPending || returnMutation.isPending || updateStatusMutation.isPending) && (
              <svg
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8v8z"
                ></path>
              </svg>
            )}
            {activeTab === "outgoing" ? "Record Outgoing Pallets" : "Record Returned Pallets"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
