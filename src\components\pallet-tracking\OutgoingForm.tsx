
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar, FileText, Truck, Clipboard } from "lucide-react";

interface OutgoingFormProps {
  formState: {
    delivery_date: string;
    delivery_note: string;
    vehicle_registration: string;
    destination: string;
    pallets_loaded: string;
    comments: string;
  };
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

export const OutgoingForm = ({ formState, handleChange }: OutgoingFormProps) => (
  <div className="space-y-4 mt-4">
    <div className="grid grid-cols-2 gap-4">
      <div className="grid gap-2">
        <Label htmlFor="delivery_date">
          <Calendar size={16} className="inline mr-2" />
          Delivery Date
        </Label>
        <Input id="delivery_date" type="date" value={formState.delivery_date} onChange={handleChange} />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="delivery_note">
          <FileText size={16} className="inline mr-2" />
          Delivery Note #
        </Label>
        <Input id="delivery_note" placeholder="e.g. DN-4501" value={formState.delivery_note} onChange={handleChange} />
      </div>
    </div>
    <div className="grid grid-cols-2 gap-4">
      <div className="grid gap-2">
        <Label htmlFor="vehicle_registration">
          <Truck size={16} className="inline mr-2" />
          Vehicle Registration
        </Label>
        <Input id="vehicle_registration" placeholder="e.g. AB12 CDE" value={formState.vehicle_registration} onChange={handleChange} />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="pallets_loaded">
          <Clipboard size={16} className="inline mr-2" />
          Pallets Loaded
        </Label>
        <Input id="pallets_loaded" type="number" min="1" placeholder="Number of pallets" value={formState.pallets_loaded} onChange={handleChange} />
      </div>
    </div>
    <div className="grid gap-2">
      <Label htmlFor="destination">
        Destination
      </Label>
      <Input id="destination" placeholder="Delivery destination" value={formState.destination} onChange={handleChange} />
    </div>
    <div className="grid gap-2">
      <Label htmlFor="comments">
        Comments (Optional)
      </Label>
      <Textarea id="comments" placeholder="Add any additional notes about this delivery" value={formState.comments} onChange={handleChange} />
    </div>
  </div>
);
