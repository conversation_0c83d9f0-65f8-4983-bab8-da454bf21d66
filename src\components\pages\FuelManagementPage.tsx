import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Fuel, AlertTriangle, TrendingDown, Truck, Droplet, Loader2 } from "lucide-react";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { toast } from "sonner";
import { FuelActionChoiceDialog } from "../dashboard/dialogs/FuelActionChoiceDialog";
import { RecordFuelDispensingDialog } from "../dashboard/dialogs/RecordFuelDispensingDialog";
import { RecordFuelDeliveryDialog } from "../dashboard/dialogs/RecordFuelDeliveryDialog";
import { getFuelBunkers, type FuelBunker } from "@/data/fuelBunkersData";
import { BalanceChart } from "../fuel/BalanceChart";
import { FuelTransactionReport } from "../fuel/FuelTransactionReport";
import { useQ<PERSON>y } from "@tanstack/react-query";
import { DispenseFuelDialog } from "../dashboard/dialogs/DispenseFuelDialog";

// Mock assets data, similar to QuickAccessCards
const mockAssets = [
  { id: "asset1", name: "Kiln A" },
  { id: "asset2", name: "Kiln B" },
  { id: "asset3", name: "Forklift FL001" },
  { id: "asset4", name: "Generator G002" },
  { id: "asset5", name: "Truck T003" },
];

export const FuelManagementPage = () => {
  // Add new state for shared dialogs
  const [isFuelActionChoiceOpen, setIsFuelActionChoiceOpen] = useState(false);
  const [isRecordDispensingOpen, setIsRecordDispensingOpen] = useState(false);
  const [isRecordDeliveryOpen, setIsRecordDeliveryOpen] = useState(false);

  const { data: bunkers = [], isLoading: isLoadingBunkers } = useQuery<FuelBunker[]>({
    queryKey: ['fuelBunkers'],
    queryFn: getFuelBunkers,
  });

  const fuelData = [
    { date: 'Mon', consumption: 450, delivery: 0 },
    { date: 'Tue', consumption: 420, delivery: 0 },
    { date: 'Wed', consumption: 380, delivery: 2000 },
    { date: 'Thu', consumption: 460, delivery: 0 },
    { date: 'Fri', consumption: 490, delivery: 0 },
    { date: 'Sat', consumption: 350, delivery: 0 },
    { date: 'Sun', consumption: 280, delivery: 0 },
  ];

  const getBunkerStatus = (bunker: { current_level: number; capacity: number }) => {
    const percentage = (bunker.current_level / bunker.capacity) * 100;
    if (percentage <= 25) return 'Critical';
    if (percentage <= 40) return 'Low';
    if (percentage >= 95) return 'Full';
    return 'Normal';
  };

  const bunkersWithStatus = bunkers.map(b => ({
    ...b,
    status: getBunkerStatus(b),
  }));

  const totalCapacity = bunkers.reduce((acc, b) => acc + b.capacity, 0);
  const currentStock = bunkers.reduce((acc, b) => acc + b.current_level, 0);
  const criticalLevelsCount = bunkersWithStatus.filter(
    b => b.status === 'Critical' || b.status === 'Low'
  ).length;

  // Map bunkers to the format expected by dialogs
  const dialogBunkers = bunkers.map(b => ({ id: b.id, name: b.name }));

  // Handler functions for the new dialog flow
  const handleOpenFuelActionChoice = () => {
    toast.dismiss();
    setIsFuelActionChoiceOpen(true);
  };

  const openRecordDispensingDialog = () => {
    setIsFuelActionChoiceOpen(false);
    setIsRecordDispensingOpen(true);
  };

  const openRecordDeliveryDialog = () => {
    setIsFuelActionChoiceOpen(false);
    setIsRecordDeliveryOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Fuel Management</h1>
          <p className="text-slate-600">Monitor fuel consumption, deliveries, and inventory</p>
        </div>
        <div className="flex gap-2">
          <Button 
            className="bg-slate-800 hover:bg-slate-700"
            onClick={handleOpenFuelActionChoice} // Updated onClick
          >
            <Droplet size={20} className="mr-2" />
            Record Dispensing
          </Button>
          <Button 
            className="bg-slate-800 hover:bg-slate-700"
            onClick={handleOpenFuelActionChoice} // Updated onClick
          >
            <Truck size={20} className="mr-2" />
            Record Delivery {/* Text changed from "Schedule Delivery" to "Record Delivery" for consistency with dialog */}
          </Button>
        </div>
      </div>

      {isLoadingBunkers ? (
        <div className="flex justify-center items-center py-16">
            <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
        </div>
      ) : (
      <>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-slate-800">{totalCapacity.toLocaleString()}L</div>
                  <p className="text-slate-600">Total Capacity</p>
                </div>
                <Fuel className="h-8 w-8 text-slate-400" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{currentStock.toLocaleString()}L</div>
                  <p className="text-slate-600">Current Stock</p>
                </div>
                <TrendingDown className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-red-600">{criticalLevelsCount}</div>
                  <p className="text-slate-600">Critical Levels</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-400" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-2xl font-bold text-green-600">430L</div>
                  <p className="text-slate-600">Daily Average</p>
                </div>
                <Fuel className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Consumption vs Delivery</CardTitle>
              <p className="text-sm text-slate-600">Fuel usage and delivery tracking</p>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={fuelData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="consumption" fill="#ef4444" name="Consumption (L)" />
                    <Bar dataKey="delivery" fill="#22c55e" name="Delivery (L)" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Fuel Bunker Status</CardTitle>
              <p className="text-sm text-slate-600">Current levels and capacity</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {bunkersWithStatus.map((bunker) => (
                  <div key={bunker.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium">{bunker.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        bunker.status === 'Critical' 
                          ? 'bg-red-100 text-red-800' 
                          : bunker.status === 'Low'
                          ? 'bg-yellow-100 text-yellow-800'
                          : bunker.status === 'Full'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {bunker.status}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm text-slate-600 mb-2">
                      <span>{bunker.current_level.toLocaleString()}L / {bunker.capacity.toLocaleString()}L</span>
                      <span>{Math.round((bunker.current_level / bunker.capacity) * 100)}%</span>
                    </div>
                    <div className="w-full bg-slate-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          bunker.status === 'Critical' ? 'bg-red-500' :
                          bunker.status === 'Low' ? 'bg-yellow-500' :
                          bunker.status === 'Full' ? 'bg-blue-500' : 'bg-green-500'
                        }`}
                        style={{width: `${(bunker.current_level / bunker.capacity) * 100}%`}}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <BalanceChart />
        <FuelTransactionReport />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <p className="text-sm text-slate-600">Common fuel management tasks</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card 
              className="border-2 border-dashed hover:border-slate-400 transition-colors cursor-pointer" 
              onClick={handleOpenFuelActionChoice} // Updated onClick
            >
              <CardContent className="p-6 flex flex-col items-center justify-center text-center h-40">
                <Droplet size={40} className="mb-4 text-blue-500" />
                <h3 className="text-lg font-medium mb-1">Record Fuel Dispensing</h3>
                <p className="text-sm text-slate-500">Log fuel usage for kilns or vehicles</p>
              </CardContent>
            </Card>
            
            <Card 
              className="border-2 border-dashed hover:border-slate-400 transition-colors cursor-pointer" 
              onClick={handleOpenFuelActionChoice} // Updated onClick
            >
              <CardContent className="p-6 flex flex-col items-center justify-center text-center h-40">
                <Truck size={40} className="mb-4 text-green-500" />
                <h3 className="text-lg font-medium mb-1">Record Fuel Delivery</h3> 
                {/* Text changed from "Schedule Fuel Delivery" */}
                <p className="text-sm text-slate-500">Plan upcoming fuel deliveries</p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Add new shared dialogs */}
      <FuelActionChoiceDialog
        isOpen={isFuelActionChoiceOpen}
        onClose={() => setIsFuelActionChoiceOpen(false)}
        onRecordDispensing={openRecordDispensingDialog}
        onRecordDelivery={openRecordDeliveryDialog}
      />

      <DispenseFuelDialog
        isOpen={isRecordDispensingOpen}
        onClose={() => setIsRecordDispensingOpen(false)}
      />

      <RecordFuelDeliveryDialog
        isOpen={isRecordDeliveryOpen}
        onClose={() => setIsRecordDeliveryOpen(false)}
        bunkers={bunkers.map(b => ({ id: b.id, name: b.name }))}
      />
    </div>
  );
};
